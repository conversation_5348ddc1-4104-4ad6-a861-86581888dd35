/**
 * 任务数据管理Hook
 * 管理任务列表的加载、搜索、分页等状态
 */

import { useState, useCallback, useEffect } from 'react';
import { App } from 'antd';
import type { TaskBasic, TaskBasicSearchParams } from '../types';
import { TaskService } from '../services';
import { DEFAULT_PAGINATION } from '../constants';

export interface UseTaskDataOptions {
  /** 是否自动加载数据 */
  autoLoad?: boolean;
  /** 初始搜索参数 */
  initialParams?: TaskBasicSearchParams;
}

export interface UseTaskDataReturn {
  /** 任务列表数据 */
  data: TaskBasic[];
  /** 数据加载状态 */
  loading: boolean;
  /** 数据总数 */
  total: number;
  /** 分页状态 */
  pagination: {
    current: number;
    page_size: number;
  };
  /** 搜索参数 */
  searchParams: TaskBasicSearchParams;
  /** 加载数据方法 */
  loadData: (customParams?: Partial<TaskBasicSearchParams>) => Promise<void>;
  /** 刷新数据方法 */
  refreshData: () => Promise<void>;
  /** 重置数据方法 */
  resetData: () => void;
  /** 更新搜索参数方法 */
  updateSearchParams: (params: TaskBasicSearchParams) => void;
  /** 更新分页方法 */
  updatePagination: (current: number, pageSize: number) => void;
}

/**
 * 任务数据管理Hook
 */
export function useTaskData(options: UseTaskDataOptions = {}): UseTaskDataReturn {
  const { autoLoad = true, initialParams = {} } = options;
  const { message } = App.useApp();

  // 状态管理
  const [data, setData] = useState<TaskBasic[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [pagination, setPagination] = useState(DEFAULT_PAGINATION);
  const [searchParams, setSearchParams] = useState<TaskBasicSearchParams>(initialParams);

  // 加载数据方法
  const loadData = useCallback(
    async (customParams?: Partial<TaskBasicSearchParams>) => {
      setLoading(true);

      try {
        // 使用传入的自定义参数，如果没有则使用当前状态
        const currentPage = customParams?.current ?? pagination.current;
        const currentPageSize = customParams?.page_size ?? pagination.page_size;

        // 合并参数
        const params = {
          ...searchParams,
          current: currentPage,
          page_size: currentPageSize,
          ...customParams,
        };

        const response = await TaskService.getTasks(params);

        // 检查返回数据
        if (!response || !response.data || response.data == null) {
          console.warn('获取任务数据失败：返回数据为空');
          message.warning('未获取到任务数据');
          setData([]);
          setTotal(0);
          return;
        }

        // 检查数据格式
        if (!Array.isArray(response.data)) {
          console.warn('获取任务数据失败：数据格式不正确');
          message.warning('数据格式错误');
          setData([]);
          setTotal(0);
          return;
        }

        setData(response.data);
        setTotal(response.total || 0);
      } catch (error) {
        message.error('加载数据失败');
        console.error('加载数据失败:', error);
        setData([]);
        setTotal(0);
      } finally {
        setLoading(false);
      }
    },
    [pagination, searchParams, message]
  );

  // 刷新数据方法
  const refreshData = useCallback(async () => {
    await loadData();
  }, [loadData]);

  // 重置数据方法
  const resetData = useCallback(() => {
    setData([]);
    setTotal(0);
    setPagination(DEFAULT_PAGINATION);
    setSearchParams({});
  }, []);

  // 更新搜索参数方法
  const updateSearchParams = useCallback((params: TaskBasicSearchParams) => {
    setSearchParams(params);
  }, []);

  // 更新分页方法
  const updatePagination = useCallback((current: number, pageSize: number) => {
    setPagination({
      current: current as typeof DEFAULT_PAGINATION.current,
      page_size: pageSize as typeof DEFAULT_PAGINATION.page_size,
    });
  }, []);

  // 自动加载数据
  useEffect(() => {
    if (autoLoad) {
      loadData();
    }
  }, [autoLoad, loadData]);

  return {
    data,
    loading,
    total,
    pagination,
    searchParams,
    loadData,
    refreshData,
    resetData,
    updateSearchParams,
    updatePagination,
  };
}
