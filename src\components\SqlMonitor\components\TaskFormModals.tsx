import React, { useEffect } from 'react';
import { Modal, Form, Input, Select, Row, Col, Button } from 'antd';

// 导入重构后的模块
import type { TaskAlert, DBConnection, AlertSend } from '../types';
import { DB_TYPE_OPTIONS, ALERT_TYPE_OPTIONS, ALERT_SEVERITY_OPTIONS, SEND_TYPE_OPTIONS } from '../constants';

const { Option } = Select;
const { TextArea } = Input;

interface AlertModalProps {
  visible: boolean;
  editingData?: TaskAlert;
  onCancel: () => void;
  onSubmit: (data: TaskAlert) => void;
}

interface DbConnectionModalProps {
  visible: boolean;
  editingData?: DBConnection;
  onCancel: () => void;
  onSubmit: (data: DBConnection) => void;
}

interface AlertSendModalProps {
  visible: boolean;
  editingData?: AlertSend;
  onCancel: () => void;
  onSubmit: (data: AlertSend) => void;
}

// 告警Modal
export const AlertModal: React.FC<AlertModalProps> = ({ visible, editingData, onCancel, onSubmit }) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (visible && editingData) {
      form.setFieldsValue(editingData);
    } else if (visible) {
      form.resetFields();
    }
  }, [visible, editingData, form]);

  const handleSubmit = async (values: any) => {
    const alertData: TaskAlert = {
      id: editingData?.id || Date.now(),
      ...values,
      values: values.type === 'isEqual' ? values.values || [] : [],
      create_time: editingData?.create_time || new Date().toISOString(),
      update_time: new Date().toISOString(),
    };
    onSubmit(alertData);
  };

  return (
    <Modal title={editingData ? '编辑告警' : '新增告警'} open={visible} onCancel={onCancel} footer={null} width={600}>
      <Form form={form} layout='vertical' onFinish={handleSubmit}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label='告警名称' name='name' rules={[{ required: true, message: '请输入告警名称' }]}>
              <Input placeholder='请输入告警名称' />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label='告警级别' name='severity' rules={[{ required: true, message: '请选择告警级别' }]}>
              <Select placeholder='请选择告警级别'>
                {ALERT_SEVERITY_OPTIONS.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item label='SQL语句' name='sql' rules={[{ required: true, message: '请输入SQL语句' }]}>
              <TextArea rows={4} placeholder='请输入SQL语句' />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label='告警类型' name='type' rules={[{ required: true, message: '请选择告警类型' }]}>
              <Select placeholder='请选择告警类型'>
                {ALERT_TYPE_OPTIONS.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label='触发值' name='values' dependencies={['type']}>
              <Select mode='tags' placeholder='请输入触发值' disabled={form.getFieldValue('type') !== 'isEqual'} />
            </Form.Item>
          </Col>
        </Row>
        <div className='flex justify-end gap-3 pt-4 border-t border-gray-200'>
          <Button onClick={onCancel}>取消</Button>
          <Button type='primary' htmlType='submit'>
            {editingData ? '更新' : '创建'}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

// 数据库连接Modal
export const DbConnectionModal: React.FC<DbConnectionModalProps> = ({ visible, editingData, onCancel, onSubmit }) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (visible && editingData) {
      form.setFieldsValue(editingData);
    } else if (visible) {
      form.resetFields();
    }
  }, [visible, editingData, form]);

  const handleSubmit = async (values: any) => {
    const dbData: DBConnection = {
      id: editingData?.id || Date.now(),
      ...values,
      create_time: editingData?.create_time || new Date().toISOString(),
      update_time: new Date().toISOString(),
    };
    onSubmit(dbData);
  };

  return (
    <Modal title={editingData ? '编辑数据库连接' : '新增数据库连接'} open={visible} onCancel={onCancel} footer={null} width={800}>
      <Form form={form} layout='vertical' onFinish={handleSubmit}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label='连接名称' name='name' rules={[{ required: true, message: '请输入连接名称' }]}>
              <Input placeholder='请输入连接名称' />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label='数据库类型' name='db_type' rules={[{ required: true, message: '请选择数据库类型' }]}>
              <Select placeholder='请选择数据库类型'>
                {DB_TYPE_OPTIONS.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label='主机地址' name='host' rules={[{ required: true, message: '请输入主机地址' }]}>
              <Input placeholder='请输入主机地址' />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label='端口' name='port' rules={[{ required: true, message: '请输入端口' }]}>
              <Input placeholder='请输入端口' />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label='用户名' name='user' rules={[{ required: true, message: '请输入用户名' }]}>
              <Input placeholder='请输入用户名' />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label='密码' name='passwd' rules={[{ required: true, message: '请输入密码' }]}>
              <Input.Password placeholder='请输入密码' />
            </Form.Item>
          </Col>
        </Row>
        <div className='flex justify-end gap-3 pt-4 border-t border-gray-200'>
          <Button onClick={onCancel}>取消</Button>
          <Button type='primary' htmlType='submit'>
            {editingData ? '更新' : '创建'}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

// 告警发送Modal
export const AlertSendModal: React.FC<AlertSendModalProps> = ({ visible, editingData, onCancel, onSubmit }) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (visible && editingData) {
      form.setFieldsValue(editingData);
    } else if (visible) {
      form.resetFields();
    }
  }, [visible, editingData, form]);

  const handleSubmit = async (values: any) => {
    const sendData: AlertSend = {
      id: editingData?.id || Date.now(),
      ...values,
      create_time: editingData?.create_time || new Date().toISOString(),
      update_time: new Date().toISOString(),
    };
    onSubmit(sendData);
  };

  return (
    <Modal title={editingData ? '编辑告警发送' : '新增告警发送'} open={visible} onCancel={onCancel} footer={null} width={600}>
      <Form form={form} layout='vertical' onFinish={handleSubmit}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label='发送名称' name='name' rules={[{ required: true, message: '请输入发送名称' }]}>
              <Input placeholder='请输入发送名称' />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label='发送类型' name='type' rules={[{ required: true, message: '请选择发送类型' }]}>
              <Select placeholder='请选择发送类型'>
                {SEND_TYPE_OPTIONS.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>
        <div className='flex justify-end gap-3 pt-4 border-t border-gray-200'>
          <Button onClick={onCancel}>取消</Button>
          <Button type='primary' htmlType='submit'>
            {editingData ? '更新' : '创建'}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

// 导出组合对象
export const TaskFormModals = {
  AlertModal,
  DbConnectionModal,
  AlertSendModal,
};

export default TaskFormModals;
